'use client';

import React, { useState, useContext } from 'react'
import Image from 'next/image'
import SingInDailog from './singInDailog'
import { UserDetailContext } from '@/context/userDetailContext'

const Header = () => {
  const [openDialog, setOpenDialog] = useState(false);
const {userDetails, setUserDetails} = useContext(UserDetailContext);
  return (
    <div className="p-4 flex justify-between items-center">
        <Image src="/logo-dark-styled.png" alt="logo" width={150} height={150} className="relative" priority />
        {!userDetails?.name && <div className="flex gap-5">
             <button 
                onClick={() => setOpenDialog(true)}
                className="relative inline-flex h-10 overflow-hidden rounded-full p-[1px] focus:outline-none focus:ring-2 focus:ring-slate-400 focus:ring-offset-2 focus:ring-offset-slate-50 group"
             >
                <span className="absolute inset-[-1000%] animate-[spin_2s_linear_infinite] bg-[conic-gradient(from_90deg_at_50%_50%,#E2E8F0_0%,#94A3B8_50%,#E2E8F0_100%)]" />
                <span className="inline-flex h-full w-full cursor-pointer items-center justify-center rounded-full bg-slate-950 px-6 py-1 text-sm font-medium text-white backdrop-blur-3xl transition-all duration-300 group-hover:bg-white group-hover:text-black">
                  Sign in
                </span>
             </button>
        </div>}
        <SingInDailog openDialog={openDialog} onClose={() => setOpenDialog(false)} />
    </div>
  )
}

export default Header