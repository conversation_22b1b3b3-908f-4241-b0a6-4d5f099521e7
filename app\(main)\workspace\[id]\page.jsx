'use client'
import React, { useState, useContext } from 'react'
import { BeamsBackground } from '@/components/animate-ui/backgrounds/beams-background'
import Image from 'next/image'
import Link from 'next/link'
import ChatView from '@/components/custom/chatView'
import CodeView from '@/components/custom/codeView'

const WorkspacePage = () => {
  return (
    <div className="min-h-screen">
      <BeamsBackground intensity="strong" />
      <header className="fixed top-0 left-0 right-0 p-4 z-10">
        <Link href="/">
          <Image src="/logo-dark-styled.png" alt="logo" width={150} height={150} className="relative cursor-pointer" priority />
        </Link>
      </header>
      <div className="pt-24 px-4">
        <div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
          <ChatView />
          <div className='col-span-3'>
            <CodeView />
          </div>
        </div>
      </div>
    </div>
  )
}

export default WorkspacePage