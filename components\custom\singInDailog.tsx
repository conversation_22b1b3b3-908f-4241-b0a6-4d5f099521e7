'use client';

import * as React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogFooter,
} from "@/components/ui/dialog"
import { X, Github, Mail } from 'lucide-react';
import lookup from '@/data/lookup';
import { useRouter } from 'next/navigation';
import { useGoogleLogin } from '@react-oauth/google';
import axios from 'axios';
import { useContext } from 'react';
import { UserDetailContext, UserDetails } from '@/context/userDetailContext';
import { useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { v4 as uuidv4 } from 'uuid';


interface SignInDialogProps {
    openDialog: boolean;
    onClose?: () => void;
}

interface OAuthCallbackEvent extends MessageEvent {
    data: {
        type: string;
        user: UserDetails;
        token: string;
    };
}

function SingInDailog({ openDialog, onClose }: SignInDialogProps) {
    const router = useRouter();
    const userDetailContext = useContext(UserDetailContext);
    const createUser = useMutation(api.users.createUser);

    const handleGoogleSignIn = useGoogleLogin({
        onSuccess: async (tokenResponse) => {
            try {
                console.log(tokenResponse);
                const userInfo = await axios.get<UserDetails>(
                    'https://www.googleapis.com/oauth2/v3/userinfo',
                    { headers: { Authorization: `Bearer ${tokenResponse.access_token}` } },
                );

                console.log(userInfo);
                const user = userInfo.data;

                await createUser({
                    name: user.name,
                    email: user.email,
                    image: user.picture || '/default-avatar.png',
                    uid: uuidv4(),
                });

                if(typeof window !== 'undefined'){
                    localStorage.setItem('user', JSON.stringify(user));
                }

                if (userDetailContext) {
                    userDetailContext.setUserDetails(user);
                }

                onClose?.();
            } catch (error) {
                console.error('Error during Google sign in:', error);
            }
        },
        onError: (errorResponse: any) => console.error('Google login error:', errorResponse),
    });

    const handleGithubSignIn = () => {
        const width = 500;
        const height = 600;
        const left = window.screenX + (window.outerWidth - width) / 2;
        const top = window.screenY + (window.outerHeight - height) / 2;

        const popup = window.open(
            `https://github.com/login/oauth/authorize?` +
            `client_id=${process.env.NEXT_PUBLIC_GITHUB_CLIENT_ID}` +
            `&redirect_uri=${window.location.origin}/api/auth/github/callback` +
            `&scope=read:user user:email`,
            'GitHub Sign In',
            `width=${width},height=${height},left=${left},top=${top}`
        );

        const handleMessage = (event: MessageEvent) => {
            if (event.origin !== window.location.origin) return;
            if ((event as OAuthCallbackEvent).data.type === 'oauth_callback') {
                const { user, token } = (event as OAuthCallbackEvent).data;
                // Handle successful sign in
                console.log('Signed in user:', user);
                if (userDetailContext) {
                    userDetailContext.setUserDetails(user);
                }
                onClose?.();
                // Remove the event listener
                window.removeEventListener('message', handleMessage);
                // Redirect to homepage
                router.push('/');
                router.refresh();
            }
        };

        window.addEventListener('message', handleMessage);
    };

    return (
        <Dialog open={openDialog} onOpenChange={(open) => !open && onClose?.()}>
            <DialogContent className="sm:max-w-md rounded-xl border border-white/10 bg-black/90 backdrop-blur-xl shadow-2xl">
                <DialogHeader className="space-y-4">
                    <DialogTitle className="text-center font-bold text-2xl text-white tracking-tight">{lookup.SIGNIN_HEADING}</DialogTitle>
                    <DialogDescription className="text-center text-white/70 text-base">
                        {lookup.SIGNIN_SUBHEADING}
                    </DialogDescription>
                </DialogHeader>
                
                <div className="flex flex-col gap-4 py-5">
                    <Button 
                        className="bg-white text-black hover:bg-white/90 transition-all w-full flex items-center justify-center gap-3 py-6 rounded-lg shadow-md hover:shadow-lg"
                        onClick={() => handleGoogleSignIn()}
                    >
                        <svg className="h-5 w-5" viewBox="0 0 24 24">
                            <path
                                d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                                fill="#4285F4"
                            />
                            <path
                                d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                                fill="#34A853"
                            />
                            <path
                                d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                                fill="#FBBC05"
                            />
                            <path
                                d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                                fill="#EA4335"
                            />
                        </svg>
                        <span className="font-semibold">Sign in with Google</span>
                    </Button>

                    <Button 
                        className="bg-zinc-900 text-white hover:bg-zinc-800 border border-white/20 transition-all w-full flex items-center justify-center gap-3 py-6 rounded-lg shadow-md hover:shadow-lg"
                        onClick={handleGithubSignIn}
                    >
                        <Github className="h-5 w-5" />
                        <span className="font-semibold">Sign in with GitHub</span>
                    </Button>
                </div>
                
                <p className="text-center text-white/50 text-xs mt-1 px-6 leading-relaxed mx-auto max-w-xs">
                    {lookup.SIGNIN_AGREEMENT_TEXT}
                </p>
                
                <DialogFooter className="mt-5">
                    <Button 
                        variant="outline" 
                        onClick={onClose}
                        className="bg-transparent text-white hover:bg-white/90 transition-all w-full flex items-center justify-center gap-3 py-4 rounded-md border border-white/05"
                    >
                        Cancel
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}
export default SingInDailog;