/* Custom Markdown Styles for Chat Messages */
.markdown-content {
  @apply text-gray-800;
}

.markdown-content h1 {
  @apply text-xl font-bold mb-3 text-gray-900 border-b border-gray-200 pb-2;
}

.markdown-content h2 {
  @apply text-lg font-semibold mb-2 text-gray-900;
}

.markdown-content h3 {
  @apply text-base font-medium mb-2 text-gray-800;
}

.markdown-content p {
  @apply mb-3 leading-relaxed;
}

.markdown-content ul {
  @apply list-disc list-inside mb-3 space-y-1;
}

.markdown-content ol {
  @apply list-decimal list-inside mb-3 space-y-1;
}

.markdown-content li {
  @apply mb-1;
}

.markdown-content blockquote {
  @apply border-l-4 border-blue-500 pl-4 italic text-gray-600 mb-3 bg-blue-50 py-2;
}

.markdown-content pre {
  @apply bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto border border-gray-700 mb-3;
}

.markdown-content code {
  @apply font-mono text-sm;
}

.markdown-content pre code {
  @apply text-gray-100;
}

.markdown-content :not(pre) > code {
  @apply bg-gray-100 text-gray-800 px-1 py-0.5 rounded text-sm;
}

.markdown-content table {
  @apply w-full border-collapse border border-gray-300 mb-3;
}

.markdown-content th,
.markdown-content td {
  @apply border border-gray-300 px-3 py-2 text-left;
}

.markdown-content th {
  @apply bg-gray-100 font-semibold;
}

.markdown-content a {
  @apply text-blue-600 hover:text-blue-800 underline;
}

.markdown-content strong {
  @apply font-semibold;
}

.markdown-content em {
  @apply italic;
}

/* Thinking section styling */
.thinking-section {
  @apply bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4;
}

.thinking-header {
  @apply flex items-center mb-2;
}

.thinking-dot {
  @apply w-2 h-2 bg-blue-500 rounded-full mr-2;
}

.thinking-title {
  @apply text-sm font-medium text-blue-700;
}

.thinking-content {
  @apply text-blue-800 text-sm;
}

/* Code Project styling */
.code-project {
  @apply border border-gray-200 rounded-lg p-4 mb-4 bg-gray-50;
}

.code-project-header {
  @apply flex items-center mb-3 pb-2 border-b border-gray-200;
}

.code-project-title {
  @apply text-sm font-medium text-gray-700;
}

/* v0.dev Version styling */
.version-header {
  @apply flex items-center justify-between bg-gray-100 px-4 py-2 rounded-t-lg border border-gray-200 mb-0;
}

.version-number {
  @apply text-sm font-medium text-gray-700;
}

.version-actions {
  @apply flex items-center gap-2;
}

.version-button {
  @apply px-3 py-1 text-xs font-medium rounded border;
}

.restore-button {
  @apply bg-white text-gray-600 border-gray-300 hover:bg-gray-50;
}

.view-button {
  @apply bg-blue-600 text-white border-blue-600 hover:bg-blue-700;
}

/* File status indicators */
.file-list {
  @apply bg-white border-l border-r border-b border-gray-200 rounded-b-lg p-4;
}

.file-item {
  @apply flex items-center justify-between py-2 px-3 hover:bg-gray-50 rounded;
}

.file-name {
  @apply flex items-center gap-2 text-sm text-gray-700;
}

.file-status {
  @apply text-xs font-medium px-2 py-1 rounded;
}

.status-edited {
  @apply bg-blue-100 text-blue-700;
}

.status-generated {
  @apply bg-green-100 text-green-700;
}

/* AI Loading Animation */
.ai-loading-dots {
  @apply flex gap-1;
}

.ai-loading-dot {
  @apply w-2 h-2 bg-blue-500 rounded-full animate-bounce;
}

.ai-loading-dot:nth-child(1) {
  animation-delay: 0ms;
}

.ai-loading-dot:nth-child(2) {
  animation-delay: 150ms;
}

.ai-loading-dot:nth-child(3) {
  animation-delay: 300ms;
}

/* Thinking indicator */
.thinking-indicator {
  @apply flex items-center gap-2 text-sm text-gray-600;
}

.thinking-pulse {
  @apply animate-pulse;
}

/* AI Avatar animation */
.ai-avatar-loading {
  @apply w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center;
}

.ai-avatar-inner {
  @apply w-4 h-4 rounded-full bg-white animate-pulse;
}

/* Chat image styles */
.chat-image {
  @apply max-w-xs max-h-48 object-cover rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow cursor-pointer;
}

.chat-image-container {
  @apply relative;
}

.chat-image-name {
  @apply text-xs text-gray-500 mt-1 truncate max-w-xs bg-gray-100 px-2 py-1 rounded;
}

/* Image preview in input */
.image-preview {
  @apply w-16 h-16 object-cover rounded-md border border-gray-300 shadow-sm;
}

.image-preview-container {
  @apply relative hover:scale-105 transition-transform;
}

.image-preview-container:hover .image-remove-btn {
  @apply opacity-100;
}

.image-remove-btn {
  @apply absolute -top-2 -right-2 bg-red-500 hover:bg-red-600 text-white rounded-full p-1 opacity-0 transition-all duration-200 cursor-pointer;
}
