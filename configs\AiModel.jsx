const {
      GoogleGenerativeAI,
       HarmCategory,
       HarmBlockThreshold,
} = require("@google/generative-ai");

const apiKey = process.env.NEXT_PUBLIC_GEMINI_API_KEY;
const genAI = new GoogleGenerativeAI(apiKey);

const model = genAI.getGenerativeModel({
model: "gemini-2.5-flash",
});

const generationConfig = {
    temperature: 1,
    topP: 0.95,
    topK: 40,
    maxOutputTokens: 8192,
    ResponseMimeType: "application/json",
};

const CodeGenerationConfig = {
    temperature: 1,
    topP: 0.95,
    topK: 40,
    maxOutputTokens: 8192,
    ResponseMimeType: "application/json",
};


export const chatSession = model.startChat({
   generationConfig,
 history: [
  ],
});

export const GenAiCode=model.startChat({
    generationConfig: CodeGenerationConfig,
    history: [
         ],
    });
// const result = await chatSession.sendMessage("INSERT_INPUT_HERE"};
// console.log(result.response.text());