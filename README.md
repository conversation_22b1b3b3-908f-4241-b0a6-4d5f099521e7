# Stackflow

A modern AI-powered development workspace that helps you build applications faster like bolt.new and v0.div .

## Features

- Real-time code generation with AI assistance
- Live preview of your code
- Split view with resizable panels
- Syntax highlighting for multiple languages
- Chat interface for natural interaction
- File management and organization

## Getting Started

1. Clone the repository:
```bash
git clone https://github.com/yourusername/stackflow.git
cd stackflow
```

2. Install dependencies:
```bash
npm install
# or
yarn install
# or
pnpm install
```

3. Set up environment variables:
Create a `.env.local` file in the root directory and add:
```
NEXT_PUBLIC_CONVEX_URL=your_convex_url
```

4. Start the development server:
```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

5. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Project Structure

- `/app` - Next.js app router pages and layouts
- `/components` - React components
  - `/custom` - Custom components for the workspace
  - `/ui` - Reusable UI components
- `/context` - React context providers
- `/convex` - Convex backend functions and schema
- `/lib` - Utility functions and helpers

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
