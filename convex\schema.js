import { defineSchema , defineTable} from 'convex/server';
import { v } from 'convex/values';

export default defineSchema({
    users: defineTable({
        name: v.string(),
        email: v.string(),
        image: v.string(),
        uid: v.string(),
    }),
    workspace: defineTable({
        messages: v.any(), //json object
        fileData: v.optional(v.any()),
        user: v.optional(v.id("users")),
    }),
});

