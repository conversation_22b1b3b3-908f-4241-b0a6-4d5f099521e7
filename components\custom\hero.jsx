'use client'
import React, { useRef, useState } from 'react'
import lookup from '@/data/lookup'
import { ArrowRight, Link2, ImageIcon, X, Sparkles, ListTodo, Wallet, <PERSON>mbbell, History, UserPlus } from 'lucide-react'
import Link from 'next/link'
import { useContext, useEffect } from 'react'
import { MessagesContext } from '@/context/messagesContext'
import { UserDetailContext } from '@/context/userDetailContext'
import { Dialog } from '@/components/ui/dialog'
import SingInDailog from './singInDailog'
import { useMutation, useQuery } from 'convex/react'
import { api } from '@/convex/_generated/api'
import { useRouter } from 'next/navigation'

    
const Hero = () => {
    const textareaRef = useRef(null);
    const [images, setImages] = useState([]);
    const [text, setText] = useState("");
    const [isDragging, setIsDragging] = useState(false);
    const [showSuggestions, setShowSuggestions] = useState(false);
    const [userInput, setUserInput] = useState("");
    const {messages, setMessages} = useContext(MessagesContext);
    const {userDetails, setUserDetails} = useContext(UserDetailContext);
    const [openDialog, setOpenDialog] = useState(false);
    const CreateWorkspace = useMutation(api.workspace.createWorkspace);
    const GetUser = useQuery(api.users.GetUser,
        { email: userDetails?.email || "" }
    );
    const router = useRouter();
    
    const onGenerate = async (input) => {
        if(!userDetails?.name)
            {
                setOpenDialog(true);
             return;
        }
        const msg = {
            id: Date.now().toString(),
            text: input,
            timestamp: Date.now()
        };
        setMessages(Array.isArray(messages) ? [...messages, msg] : [msg]);

        try {
            // Create workspace data
            const workspaceData = {
                messages: [{ role: 'user', content: input }], // Use array format for messages
            };
            
            // Add user ID if available
            if (userDetails?.email && GetUser?._id) {
                workspaceData.user = GetUser._id;
            }
            
            // Create the workspace
            const workspaceId = await CreateWorkspace(workspaceData);
            
            console.log("Workspace created successfully:", workspaceId);
            router.push('/workspace/'+workspaceId);
        } catch (error) {
            console.error("Error creating workspace:", error);
        }
    } 
    const isValidFileType = (file) => {
        return /\.(png|jpe?g|svg)$/i.test(file.name) || file.type.startsWith('image/');
    };

    const handleFileChange = (e) => {
        const files = e.target.files;
        if (files) {
            const validFiles = Array.from(files).filter(isValidFileType);
            setImages([...images, ...validFiles]);
        }
    };

    const handleDrop = (e) => {
        e.preventDefault();
        setIsDragging(false);
        
        const files = e.dataTransfer.files;
        if (files) {
            const validFiles = Array.from(files).filter(isValidFileType);
            setImages([...images, ...validFiles]);
        }
    };

    const handlePaste = async (e) => {
        const items = e.clipboardData?.items;
        if (!items) return;

        const imageItems = Array.from(items).filter(item => 
            item.type.startsWith('image/')
        );

        for (const item of imageItems) {
            const file = item.getAsFile();
            if (file) {
                // Create a proper filename for pasted images
                const timestamp = new Date().getTime();
                const newFile = new File([file], `pasted-image-${timestamp}.png`, {
                    type: file.type,
                });
                setImages(prev => [...prev, newFile]);
            }
        }
    };

    const handleDragOver = (e) => {
        e.preventDefault();
        setIsDragging(true);
    };

    const handleDragLeave = (e) => {
        e.preventDefault();
        setIsDragging(false);
    };

    const handleRemoveImage = (index) => {
        setImages((prev) => prev.filter((_, i) => i !== index));
    };

    const handleTextChange = (e) => {
        setText(e.target.value);
        setUserInput(e.target.value);
        setShowSuggestions(e.target.value.trim() !== '');
    };

    const handleSuggestionClick = (suggestion) => {
        setText(suggestion);
        setUserInput(suggestion);
        onGenerate(suggestion);
        setShowSuggestions(false);
    };

    const handleSubmit = async () => {
        try {
            // Upload all images first
            const uploadedImages = await Promise.all(
                images.map(async (file) => {
                    const formData = new FormData();
                    formData.append('file', file);

                    const response = await fetch('http://localhost:3000/upload', {
                        method: 'POST',
                        body: formData
                    });

                    if (!response.ok) {
                        throw new Error('Upload failed');
                    }

                    const data = await response.json();
                    return {
                        name: file.name,
                        url: data.url
                    };
                })
            );

            // Create final text with markdown images
            let finalText = text;
            uploadedImages.forEach(img => {
                finalText += `\n![${img.name}](${img.url})`;
            });

            // Update textarea with final text
            setText(finalText);
            
            // Clear images after successful upload
            setImages([]);

        } catch (error) {
            console.error('Error uploading files:', error);
        }
    };

    const suggestionIcons = {
        'Create ToDo App in React': <ListTodo className="w-4 h-4" />,
        'Create Budget Track App': <Wallet className="w-4 h-4" />,
        'Create Gym Managment Portal Dashboard': <Dumbbell className="w-4 h-4" />,
        'Create Quizz App On History': <History className="w-4 h-4" />,
        'Create Login Signup Screen': <UserPlus className="w-4 h-4" />
    };

  return (
        <div className="flex flex-col items-center mt-36 xl:mt-42 gap-2">
            <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold text-white text-center">{lookup.HERO_HEADING}</h1>
            <p className="text-base md:text-lg lg:text-xl text-gray-400 max-w-2xl mx-auto text-center">{lookup.HERO_DESC}</p>
            
            {/* Main textarea container */}
            <div className='p-5 border border-white/20 rounded-xl max-w-3xl w-full mt-3 bg-white/5'>
                {/* Images Preview */}
                {images.length > 0 && (
                    <div className="flex gap-2 mb-3 flex-wrap">
                        {images.map((img, i) => (
                            <div key={i} className="relative w-16 h-16 group">
                                <img
                                    src={URL.createObjectURL(img)}
                                    alt="preview"
                                    className="w-full h-full object-cover rounded-md"
                                />
                                <button
                                    onClick={() => handleRemoveImage(i)}
                                    className="absolute -top-2 -right-2 bg-black/50 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                                >
                                    <X className="h-3 w-3" />
                                </button>
                            </div>
                        ))}
                    </div>
                )}

                <div className='flex gap-3'>
                    <div className="flex-1 relative">
                        <div 
                            className={`relative ${isDragging ? 'ring-2 ring-white/50 rounded-lg' : ''}`}
                            onDrop={handleDrop}
                            onDragOver={handleDragOver}
                            onDragLeave={handleDragLeave}
                        >
                            {isDragging && (
                                <div className="absolute inset-0 bg-white/10 rounded-lg pointer-events-none" />
                            )}
                            <textarea 
                                ref={textareaRef}
                                value={text}
                                onChange={handleTextChange}
                                onPaste={handlePaste}
                                placeholder={lookup.INPUT_PLACEHOLDER}
                                className='outline-none bg-transparent w-full h-32 resize-none text-white placeholder-white/50 text-lg px-2 rounded-lg'
                            />
                        </div>
                        <label className="absolute bottom-3 left-2 cursor-pointer group" title="Upload image">
                            <input
                                type="file"
                                accept=".png,.jpg,.jpeg,.svg"
                                onChange={handleFileChange}
                                className="opacity-0"
                                multiple
                            />
                            <ImageIcon className="h-5 w-5 text-white/50 group-hover:text-white/80 transition-colors" />
                        </label>
                    </div>
                    {text.trim() !== '' && (
                        <button 
                            onClick={() => {
                                handleSubmit();
                                onGenerate(userInput);
                            }}
                            className='bg-white hover:bg-white/90 transition-colors p-3 rounded-md cursor-pointer h-12 w-12 flex items-center justify-center self-start'
                        >
                            <ArrowRight className='h-5 w-5 text-black' />
                        </button>
                    )}
                </div>
            </div>

            {/* Suggestions outside the main container */}
            <div className="flex flex-wrap justify-center gap-2 mt-4 max-w-3xl">
                {lookup.SUGGESTIONS.map((suggestion, index) => (
                    <button
                        key={index}
                        onClick={() => {
                            handleSuggestionClick(suggestion);
                        }}
                        className="flex items-center gap-2 px-4 py-2 bg-white/5 hover:bg-white/10 rounded-md text-sm text-white/70 hover:text-white transition-colors"
                    >
                        {suggestionIcons[suggestion]}
                        {suggestion}
                    </button>
                ))}
            </div>
            <SingInDailog openDialog={openDialog} onClose={() => setOpenDialog(false)} />
        </div>
    )
}

export default Hero