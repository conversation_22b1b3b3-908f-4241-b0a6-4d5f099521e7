import { NextResponse } from 'next/server';

export async function GET(request) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const code = searchParams.get('code');

    if (!code) {
      throw new Error('No code provided');
    }

    // Exchange code for access token
    const tokenResponse = await fetch('https://oauth2.googleapis.com/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        client_id: process.env.NEXT_PUBLIC_GOOGLE_AUTH_CLIENT_ID,
        client_secret: process.env.GOOGLE_CLIENT_SECRET,
        code,
        redirect_uri: process.env.NEXT_PUBLIC_GOOGLE_REDIRECT_URI || `${request.nextUrl.origin}`,
        grant_type: 'authorization_code',
      }),
    });

    const tokenData = await tokenResponse.json();

    if (!tokenResponse.ok) {
      throw new Error('Failed to exchange code for token');
    }

    // Get user info using access token
    const userResponse = await fetch('https://www.googleapis.com/oauth2/v2/userinfo', {
      headers: {
        Authorization: `Bearer ${tokenData.access_token}`,
      },
    });

    const userData = await userResponse.json();

    if (!userResponse.ok) {
      throw new Error('Failed to get user info');
    }

    // Create our user object
    const user = {
      name: userData.name,
      email: userData.email,
      image: userData.picture,
      sub: userData.id,
      provider: 'google',
    };

    // Create HTML with script to send data back to opener
    const html = `
      <html>
        <body>
          <script>
            window.opener.postMessage(
              {
                type: 'oauth_callback',
                user: ${JSON.stringify(user)},
                token: '${tokenData.access_token}'
              },
              '${request.nextUrl.origin}'
            );
            window.close();
          </script>
        </body>
      </html>
    `;

    return new NextResponse(html, {
      headers: {
        'Content-Type': 'text/html',
      },
    });

  } catch (error) {
    console.error('Google callback error:', error);
    return new NextResponse(
      `
        <html>
          <body>
            <script>
              window.close();
            </script>
          </body>
        </html>
      `,
      {
        status: 500,
        headers: {
          'Content-Type': 'text/html',
        },
      }
    );
  }
} 