"use client"

import React, { useEffect, useContext } from 'react'
import {
  SandpackProvider,
  SandpackLayout,
  SandpackCodeEditor,
  SandpackPreview,
  SandpackFileExplorer,
} from "@codesandbox/sandpack-react";
import { useState } from 'react';
import { DiffView } from './DiffView';
import axios from 'axios';
import lookup from '@/data/lookup';
import { MessagesContext } from '@/context/messagesContext';
import Prompt from '@/data/prompt';



function CodeView() {
  const [activeTab, setActiveTab] = useState('code');
  const [files, setFiles] = useState(lookup.DEFAULT_FILE);
  const [fileHistory, setFileHistory] = useState({});
  const { messages, setMessages } = useContext(MessagesContext);

  useEffect(() => {
    if (messages?.length > 0) {
      const role = messages[messages.length - 1].role;
      if (role === 'user') {
        GenerateAiCode();
      }
    }
  }, [messages]);

  const GenerateAiCode = async () => {
    const userMessage = messages[messages?.length - 1]?.content || '';
    const PROMPT = userMessage + '\n\n' + Prompt.CODE_GEN_PROMPT;

    try {
      const result = await axios.post('/api/gen-ai-code', {
        prompt: PROMPT
      });
      console.log('AI Response:', result.data);
      const aiResp = result.data;

      if (aiResp?.files) {
        const mergedFiles = { ...lookup.DEFAULT_FILE, ...aiResp.files };
        setFiles(mergedFiles);
        // Store previous version for diff view
        setFileHistory({ previous: files, current: mergedFiles });
      }
    } catch (error) {
      console.error('Error generating AI code:', error);
    }
  }

  // Mock action runner for DiffView - you may need to adjust this based on your project structure
  const mockActionRunner = {
    // Add any required methods here based on your ActionRunner interface
  };

  return (
    <div className="h-full flex flex-col">
      {/* Tab Navigation */}
      <div className='bg-gradient-to-r from-gray-900 to-gray-800 w-full p-4 border-b border-gray-600 shadow-lg'>
        <div className='flex items-center justify-center'>
          <div className='flex bg-gray-800 p-1 rounded-lg border border-gray-600 shadow-inner'>
            <button
              onClick={() => setActiveTab('code')}
              className={`px-6 py-2 rounded-md font-medium transition-all duration-300 transform ${
                activeTab === 'code'
                  ? 'text-white bg-gradient-to-r from-blue-600 to-blue-700 shadow-lg scale-105 border border-blue-500'
                  : 'text-gray-300 hover:text-white hover:bg-gray-700 hover:scale-102'
              }`}>
              <span className="flex items-center gap-2">
                <span>💻</span>
                Code
              </span>
            </button>

            <button
              onClick={() => setActiveTab('DiffView')}
              className={`px-6 py-2 rounded-md font-medium transition-all duration-300 transform ${
                activeTab === 'DiffView'
                  ? 'text-white bg-gradient-to-r from-green-600 to-green-700 shadow-lg scale-105 border border-green-500'
                  : 'text-gray-300 hover:text-white hover:bg-gray-700 hover:scale-102'
              }`}>
              <span className="flex items-center gap-2">
                <span>🔍</span>
                Diff View
              </span>
            </button>

            <button
              onClick={() => setActiveTab('preview')}
              className={`px-6 py-2 rounded-md font-medium transition-all duration-300 transform ${
                activeTab === 'preview'
                  ? 'text-white bg-gradient-to-r from-purple-600 to-purple-700 shadow-lg scale-105 border border-purple-500'
                  : 'text-gray-300 hover:text-white hover:bg-gray-700 hover:scale-102'
              }`}>
              <span className="flex items-center gap-2">
                <span>👁️</span>
                Preview
              </span>
            </button>
          </div>
        </div>
      </div>

      {/* Tab Content */}
      <div className="flex-1 overflow-hidden bg-gray-900">
        {activeTab === 'code' && (
          <div className="h-full border-t-2 border-blue-500">
            <SandpackProvider template="react" theme={'dark'} files={files}>
              <SandpackLayout>
                <SandpackFileExplorer style={{height: '90vh'}}/>
                <SandpackCodeEditor style={{height: '90vh'}} />
              </SandpackLayout>
            </SandpackProvider>
          </div>
        )}

        {activeTab === 'DiffView' && (
          <div className="h-full border-t-2 border-green-500 bg-gray-900">
            <DiffView
              fileHistory={fileHistory}
              setFileHistory={setFileHistory}
              actionRunner={mockActionRunner}
            />
          </div>
        )}

        {activeTab === 'preview' && (
          <div className="h-full border-t-2 border-purple-500">
            <SandpackProvider template="react" theme={'dark'} files={files}>
              <SandpackLayout>
                <SandpackPreview style={{height: '90vh'}} showNavigator={true}/>
              </SandpackLayout>
            </SandpackProvider>
          </div>
        )}
      </div>
    </div>
  )
}

export default CodeView