"use client"

import React from 'react'
import {
  SandpackProvider,
  SandpackLayout,
  SandpackCodeEditor,
  SandpackPreview,
  SandpackFileExplorer,
} from "@codesandbox/sandpack-react";
import { useState } from 'react';

function CodeView() {
  const [activeTab, setActiveTab] = useState('code');
    return (
        <div>
        <div className='bg-[#181818] w-full p-2 border'>
          <div className='flex items-center flex-wrap shrink-0 bg-black p-1 justify-center rounded-full
          w-[140px] gap-3 '>
            <h2  onClick={() => setActiveTab('code')}
            className={`text-sm cursor-pointer ${activeTab === 'code'
            ? 'text-blue-500 bg-blue-500 bg-opacity-25 p-1 px-2 rounded-full' : ''}`}>
              Code</h2>


            <h2
            onClick={() => setActiveTab('DiffView')}
            className={`text-sm cursor-pointer ${activeTab === 'DiffView'
            ? 'text-blue-500 bg-blue-500 bg-opacity-25 p-1 px-2 rounded-full' : ''}`}>
              DiffView</h2>


            <h2
            onClick={() => setActiveTab('preview')}
            className={`text-sm cursor-pointer ${activeTab === 'preview'
            ? 'text-blue-500 bg-blue-500 bg-opacity-25 p-1 px-2 rounded-full' : ''}`}>
              preview</h2>
          </div>
          </div>
 
            <SandpackProvider template="react" theme={'dark'}>
    <SandpackLayout>
      <SandpackFileExplorer style={{height: '90vh'}}/>
      <SandpackCodeEditor style={{height: '90vh'}} />
      <SandpackPreview style={{height: '90vh'}} />
    </SandpackLayout>
  </SandpackProvider>
        </div>
    )
}

export default CodeView