"use client";

import { motion } from "framer-motion";
import { BeamsBackground } from "@/components/animate-ui/backgrounds/beams-background";
import Hero from "@/components/custom/hero";
import Header from "@/components/custom/header";

export default function Home() {
  return (
    <BeamsBackground>
      <div className="absolute inset-0 flex flex-col">
        <Header />
        <div className="flex-1 flex items-center justify-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="w-full max-w-7xl px-4"
          >
            <Hero />
          </motion.div>
        </div>
      </div>
    </BeamsBackground>
  );
}