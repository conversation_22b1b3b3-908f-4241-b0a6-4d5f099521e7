"use client";
import { createContext, useState } from "react";

export interface UserDetails {
    name: string;
    email: string;
    image: string;
    uid: string;
    _id?: any;
    _creationTime?: number;
    picture?: string;
    sub?: string;
    [key: string]: any;
}

interface UserDetailContextType {
    userDetails: UserDetails | null;
    setUserDetails: (details: UserDetails | null) => void;
}

export const UserDetailContext = createContext<UserDetailContextType | null>(null);

export const UserDetailProvider = ({ children }: { children: React.ReactNode }) => {
    const [userDetails, setUserDetails] = useState<UserDetails | null>(null);

    return (
        <UserDetailContext.Provider value={{ userDetails, setUserDetails }}>
            {children}
        </UserDetailContext.Provider>
    );
}; 