import { chatSession } from '@/configs/AiModel';
import { NextResponse } from 'next/server';

export async function POST(req) {
    const {prompt} = await req.json();

    try {
        const result = await chatSession.sendMessage(prompt);
        const resp = result.response.text();

        // Try to parse the response as JSON, if it fails return as plain text
        try {
            const parsedResp = JSON.parse(resp);
            return NextResponse.json(parsedResp);
        } catch (parseError) {
            // If response is not valid JSON, return it wrapped in an object
            return NextResponse.json({
                files: {
                    '/App.js': {
                        code: resp
                    }
                }
            });
        }
    } catch (error) {
        console.error('AI Code Generation Error:', error);
        return NextResponse.json(
            { error: 'Failed to generate code', details: error.message },
            { status: 500 }
        );
    }
    
 }