import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

export const createWorkspace = mutation({
    args: {
        messages: v.any(),
        user: v.optional(v.id("users")),
    },
    handler: async (ctx, args) => {
        const workspaceData = {
            messages: args.messages,
        };
        
        // Add user field if provided
        if (args.user) {
            workspaceData.user = args.user;
        }
        
        const workspaceId = await ctx.db.insert("workspace", workspaceData);
        return workspaceId;
    }
})

export const GetWorkspace = query({
    args: {
       workspaceId: v.id('workspace')
    },
    handler: async (ctx, args) => {
        const result=await ctx.db.get(args.workspaceId);
        return result;
    }
})

export const updateWorkspace = mutation({
    args: {
        workspaceId: v.id('workspace'),
        messages: v.any(),
    },
    handler: async (ctx, args) => {
        const result = await ctx.db.patch(args.workspaceId, {
            messages: args.messages
        });
        return result;
    }
})