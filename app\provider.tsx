'use client'

import {BeamsBackground} from "@/components/animate-ui/backgrounds/beams-background"
import Header from "@/components/custom/header"
import Hero from "@/components/custom/hero"
import { MessagesContext } from "@/context/messagesContext";
import { useEffect, useState, useContext } from "react";
import { UserDetailProvider, UserDetailContext } from "@/context/userDetailContext";
import { GoogleOAuthProvider } from "@react-oauth/google";
import { useConvex } from "convex/react";
import { api } from "@/convex/_generated/api";
import { usePathname } from "next/navigation";

type Message = {
    id: string;
    text: string;
    timestamp: number;
}

function Provider({children}: {children: React.ReactNode}) {
    const [messages, setMessages] = useState<Message[]>([]);
    const clientId = process.env.NEXT_PUBLIC_GOOGLE_AUTH_CLIENT_ID;
    const convex = useConvex();
    const userDetailContext = useContext(UserDetailContext);
    const pathname = usePathname();

    if (!clientId) {
        throw new Error('Google OAuth client ID is not configured');
    }
    
    useEffect(() => {
        IsAuthenticated();
    }, []);
    
    const IsAuthenticated = async () => {
        if (typeof window !== 'undefined') {
            const user = JSON.parse(localStorage.getItem('user') || '{}');
            if (user.email) {
                // fetches user from database
                const result = await convex.query(api.users.GetUser, { email: user.email });
                if (result && userDetailContext) {
                    userDetailContext.setUserDetails(result);
                    console.log('User authenticated:', result);
                }
            }
        }
    }

    // Check if we're on the homepage
    const isHomepage = pathname === '/';

    return(
        <GoogleOAuthProvider clientId={clientId}>
            <UserDetailProvider>
                <MessagesContext.Provider value={{messages, setMessages}}>
                    {isHomepage ? (
                        <BeamsBackground>
                            <Header/>
                            <Hero/>
                            {children}
                        </BeamsBackground>
                    ) : (
                        children
                    )}
                </MessagesContext.Provider>
            </UserDetailProvider>
        </GoogleOAuthProvider>
    )
}

export default Provider; 