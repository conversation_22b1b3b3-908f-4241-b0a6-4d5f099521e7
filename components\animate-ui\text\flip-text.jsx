"use client";

import { motion } from 'framer-motion';
import { cn } from '../../../lib/utils';

export function FlipText({
  frontText,
  backText,
  from = 'top',
  className,
  frontClassName,
  backClassName
}) {
  const isVertical = from === 'top' || from === 'bottom';
  const rotateAxis = isVertical ? 'rotateX' : 'rotateY';
  const frontOffset = from === 'top' || from === 'left' ? '50%' : '-50%';
  const backOffset = from === 'top' || from === 'left' ? '-50%' : '50%';

  const buildVariant = (opacity, rotation, offset = null) => ({
    opacity,
    [rotateAxis]: rotation,
    ...(isVertical && offset !== null ? { y: offset } : {}),
    ...(!isVertical && offset !== null ? { x: offset } : {})
  });

  const frontVariants = {
    initial: buildVariant(1, 0, '0%'),
    hover: buildVariant(0, 90, frontOffset),
  };

  const backVariants = {
    initial: buildVariant(0, -90, backOffset),
    hover: buildVariant(1, 0, '0%'),
  };

  return (
    <motion.div
      initial="initial"
      whileHover="hover"
      className={cn("relative inline-block perspective-[1000px]", className)}
    >
      <motion.span
        variants={frontVariants}
        transition={{ type: 'spring', stiffness: 280, damping: 20 }}
        className={cn(
          "absolute inset-0 flex items-center justify-center",
          frontClassName
        )}
      >
        {frontText}
      </motion.span>
      <motion.span
        variants={backVariants}
        transition={{ type: 'spring', stiffness: 280, damping: 20 }}
        className={cn(
          "absolute inset-0 flex items-center justify-center",
          backClassName
        )}
      >
        {backText}
      </motion.span>
      <span className="invisible select-none" aria-hidden="true">
        {frontText}
      </span>
    </motion.div>
  );
} 