import { mutation } from "./_generated/server";
import { v } from "convex/values";
import { query } from "./_generated/server";

export const createUser = mutation({
    args: {
        name: v.string(),
        email: v.string(),
        image: v.string(),
        uid: v.string(),
    },
    handler: async (ctx, args) => {
        // check if user already exists
        const user = await ctx.db.query("users")
            .filter((q) => q.eq(q.field("email"), args.email))
            .collect();
        console.log("Existing users:", user);

        // if not, create user
        if (user.length === 0) {
            const result = await ctx.db.insert("users", {
                name: args.name,
                email: args.email,
                image: args.image,
                uid: args.uid,
            });
            console.log("New user created:", result);
            return result;
        }
        return user[0];
    }
});   

export const GetUser = query({
    args: {
        email: v.string(),
    },
    handler: async (ctx, args) => {
        const user = await ctx.db.query("users").filter((q) => q.eq(q.field("email"), args.email)).collect();
        return user[0];
     }
}) 
