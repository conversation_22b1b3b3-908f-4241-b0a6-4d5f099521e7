"use client";
import React, { useContext, useEffect, useCallback, useState, useRef } from 'react'
import { useParams } from 'next/navigation'
import { useConvex, useMutation } from 'convex/react'
import { api } from '@/convex/_generated/api';
import { MessagesContext } from '@/context/messagesContext';
import { UserDetailContext } from '@/context/userDetailContext';
import Image from 'next/image';
import lookup from '@/data/lookup'
import { ArrowRight, ImageIcon, X } from 'lucide-react'
import { useRouter } from 'next/navigation'
import axios from 'axios';
import Prompt from '@/data/prompt';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeHighlight from 'rehype-highlight';
import '@/styles/markdown.css';







function ChatView() {
    const { id } = useParams();
    const convex = useConvex();
    const { messages, setMessages } = useContext(MessagesContext);
    const { userDetails } = useContext(UserDetailContext);
    const updateWorkspace = useMutation(api.workspace.updateWorkspace);

    // State variables for input functionality
    const [userInput, setUserInput] = useState("");
    const [text, setText] = useState("");
    const [images, setImages] = useState([]);
    const [isDragging, setIsDragging] = useState(false);
    const [isAiLoading, setIsAiLoading] = useState(false);
    const textareaRef = useRef(null);

    /**
     * Used to get workspace data using workspaceId
     */
    const GetworkspaceData = useCallback(async () => {
        const result = await convex.query(api.workspace.GetWorkspace, {workspaceId: id});
        setMessages(result?.messages || [])
        console.log(result);
    }, [convex, id, setMessages]);

    useEffect(() => {
        id && GetworkspaceData();
    }, [id, GetworkspaceData])

    useEffect(() => {
        if (messages?.length > 0) {
           const role=messages[messages.length - 1].role;
           if(role==='user'){
            GetAiResponse();
           }
        }
    }, [messages]);

    // Debug: Log user details to check if profile picture is available
    useEffect(() => {
        console.log('User Details:', userDetails);
        console.log('User Image:', userDetails?.image);
        console.log('User Picture:', userDetails?.picture);
    }, [userDetails])

    // Auto-focus textarea when component loads
    useEffect(() => {
        if (textareaRef.current && !isAiLoading) {
            textareaRef.current.focus();
        }
    }, [isAiLoading])

    // Handler functions for input functionality
    const handleTextChange = (e) => {
        setText(e.target.value);
        setUserInput(e.target.value);
    };

    const handleFileChange = (e) => {
        const files = Array.from(e.target.files);
        setImages(prev => [...prev, ...files]);
    };

    const handleRemoveImage = (index) => {
        setImages(prev => prev.filter((_, i) => i !== index));
    };

    const handleDrop = (e) => {
        e.preventDefault();
        setIsDragging(false);
        const files = Array.from(e.dataTransfer.files).filter(file =>
            file.type.startsWith('image/')
        );
        setImages(prev => [...prev, ...files]);
    };

    const handleDragOver = (e) => {
        e.preventDefault();
        setIsDragging(true);
    };

    const handleDragLeave = (e) => {
        e.preventDefault();
        setIsDragging(false);
    };

    const handlePaste = (e) => {
        const items = Array.from(e.clipboardData.items);
        const imageFiles = items
            .filter(item => item.type.startsWith('image/'))
            .map(item => item.getAsFile())
            .filter(file => file !== null);

        if (imageFiles.length > 0) {
            setImages(prev => [...prev, ...imageFiles]);
        }
    };

    const handleKeyDown = (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            if ((text.trim() || images.length > 0) && !isAiLoading) {
                handleSubmit();
            }
        }
    };

    // Convert image file to base64
    const fileToBase64 = (file) => {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.readAsDataURL(file);
            reader.onload = () => {
                // Remove the data:image/jpeg;base64, prefix
                const base64 = reader.result.split(',')[1];
                resolve({
                    inlineData: {
                        data: base64,
                        mimeType: file.type
                    }
                });
            };
            reader.onerror = error => reject(error);
        });
    };

    const handleSubmit = async () => {
        if ((text.trim() || images.length > 0) && !isAiLoading) {
            try {
                // Process images to base64 for AI
                const processedImages = await Promise.all(
                    images.map(async (file) => await fileToBase64(file))
                );

                // Create user message with text and images
                const userMessage = {
                    role: 'user',
                    content: text.trim() || 'Image uploaded',
                    images: images.map(img => ({
                        name: img.name,
                        type: img.type,
                        size: img.size,
                        url: URL.createObjectURL(img) // For display purposes
                    })),
                    processedImages: processedImages, // For AI processing
                    timestamp: Date.now()
                };

                // Add user message to messages
                const updatedMessages = [...messages, userMessage];
                setMessages(updatedMessages);

                // Update workspace in database with user message
                if (id) {
                    try {
                        // Don't store processedImages in database (too large)
                        const dbMessage = { ...userMessage };
                        delete dbMessage.processedImages;

                        await updateWorkspace({
                            workspaceId: id,
                            messages: [...messages, dbMessage]
                        });
                    } catch (error) {
                        console.error('Error updating workspace:', error);
                    }
                }

                // Clear input
                setText('');
                setUserInput('');
                setImages([]);

                console.log('User message submitted:', text, 'with', images.length, 'images');
            } catch (error) {
                console.error('Error processing images:', error);
            }
        }
    };



     //API CALL
     const GetAiResponse=async()=>{
        setIsAiLoading(true);
        try {
            // Create a proper prompt from the last user message
            const lastMessage = messages[messages.length - 1];
            const userPrompt = lastMessage?.content || lastMessage?.text || '';
            const PROMPT = userPrompt + '\n\n' + Prompt.CHAT_PROMPT;

            // Prepare API payload
            const apiPayload = {
                prompt: PROMPT
            };

            // Add images if they exist in the last message
            if (lastMessage?.processedImages && lastMessage.processedImages.length > 0) {
                apiPayload.images = lastMessage.processedImages;
            }

            const result = await axios.post('/api/ai_chat', apiPayload);

            // Add AI response to messages
            const aiMessage = {
                role: 'assistant',
                content: result.data.result,
                timestamp: Date.now()
            };

            const updatedMessages = [...messages, aiMessage];
            setMessages(updatedMessages);

            // Update workspace in database
            if (id) {
                await updateWorkspace({
                    workspaceId: id,
                    messages: updatedMessages
                });
            }

            console.log('AI Response:', result.data.result);
        } catch (error) {
            console.error('Error getting AI response:', error);
            // Add error message to chat
            const errorMessage = {
                role: 'assistant',
                content: 'Sorry, I encountered an error while processing your request. Please try again.',
                timestamp: Date.now()
            };
            setMessages(prevMessages => [...prevMessages, errorMessage]);
        } finally {
            setIsAiLoading(false);
        }
     }
    
    return (
        <div className="h-[calc(100vh-6rem)] bg-white/30 backdrop-blur-sm rounded-lg flex flex-col">
            {/* Chat Messages Section */}
            <div className="flex-1 overflow-hidden flex flex-col p-4">
                <h2 className="text-xl font-semibold text-gray-800 mb-4">Chat</h2>
                <div className="flex-1 overflow-y-auto space-y-2 pb-4">
                    {messages && messages.length > 0 ? (
                        messages.map((msg, index) => (
                            <div
                                key={index}
                                className="p-3 rounded-lg mb-2 bg-gray-100/50 backdrop-blur-sm border border-gray-200/30"
                            >
                                <div className="flex items-center gap-3 mb-4">
                                    {msg?.role === 'user' && userDetails && (userDetails.image || userDetails.picture) ? (
                                        <Image
                                            src={userDetails.image || userDetails.picture}
                                            alt={userDetails.name || "User Image"}
                                            width={35}
                                            height={35}
                                            className="rounded-full border-2 border-blue-400/30 object-cover"
                                        />
                                    ) : (
                                        <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                                            <span className="text-xs font-bold text-white">
                                                {msg?.role === 'user' && userDetails?.name
                                                    ? userDetails.name.charAt(0).toUpperCase()
                                                    : (msg.role || 'User').charAt(0).toUpperCase()
                                                }
                                            </span>
                                        </div>
                                    )}
                                    <div>
                                        <span className="text-sm font-semibold text-blue-400">
                                            {msg?.role === 'user' && userDetails?.name
                                                ? userDetails.name
                                                : (msg.role || 'User')
                                            }
                                        </span>
                                        <div className="text-xs text-slate-500 mt-0.5">
                                            {new Date().toLocaleTimeString()}
                                        </div>
                                    </div>
                                </div>
                                <div className="text-gray-800 max-w-none">
                                    {/* Display images if they exist */}
                                    {msg.images && msg.images.length > 0 && (
                                        <div className="mb-3 flex flex-wrap gap-3">
                                            {msg.images.map((img, imgIndex) => (
                                                <div key={imgIndex} className="chat-image-container">
                                                    <img
                                                        src={img.url}
                                                        alt={img.name || 'Uploaded image'}
                                                        className="chat-image"
                                                        onClick={() => window.open(img.url, '_blank')}
                                                    />
                                                    <div className="chat-image-name">
                                                        {img.name}
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    )}

                                    {/* Display text content */}
                                    {(msg.content || msg.text) && (
                                        msg.role === 'assistant' ? (
                                            <ReactMarkdown
                                                remarkPlugins={[remarkGfm]}
                                                rehypePlugins={[rehypeHighlight]}
                                                className="markdown-content"
                                            >
                                                {msg.content || msg.text}
                                            </ReactMarkdown>
                                        ) : (
                                            <div className="leading-relaxed">{msg.content || msg.text}</div>
                                        )
                                    )}
                                </div>
                            </div>
                        ))
                    ) : (
                        <div className="text-gray-500 text-center py-8">
                            No messages yet
                        </div>
                    )}

                    {/* AI Loading Indicator */}
                    {isAiLoading && (
                        <div className="p-3 rounded-lg mb-2 bg-gray-100/50 backdrop-blur-sm border border-gray-200/30">
                            <div className="flex items-center gap-3 mb-4">
                                <div className="ai-avatar-loading">
                                    <div className="ai-avatar-inner"></div>
                                </div>
                                <div>
                                    <span className="text-sm font-semibold text-blue-400">
                                        AI Assistant
                                    </span>
                                    <div className="text-xs text-slate-500 mt-0.5">
                                        Thinking...
                                    </div>
                                </div>
                            </div>
                            <div className="text-gray-800 max-w-none">
                                <div className="thinking-indicator">
                                    <div className="ai-loading-dots">
                                        <div className="ai-loading-dot"></div>
                                        <div className="ai-loading-dot"></div>
                                        <div className="ai-loading-dot"></div>
                                    </div>
                                    <span className="thinking-pulse">
                                        Generating response...
                                    </span>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            </div>

            {/* Input Section - Fixed at Bottom */}
            <div className="border-t border-white/20 p-4 bg-white/5 backdrop-blur-sm">
                <div className='border border-white/20 rounded-xl w-full bg-white/5 p-4'>
                    {/* Images Preview */}
                    {images.length > 0 && (
                        <div className="flex gap-3 mb-4 flex-wrap">
                            {images.map((img, i) => (
                                <div key={i} className="image-preview-container">
                                    <img
                                        src={URL.createObjectURL(img)}
                                        alt="preview"
                                        className="image-preview"
                                    />
                                    <button
                                        onClick={() => handleRemoveImage(i)}
                                        className="image-remove-btn"
                                        title="Remove image"
                                    >
                                        <X className="h-3 w-3" />
                                    </button>
                                    <div className="text-xs text-white/70 mt-1 truncate max-w-16 text-center">
                                        {img.name}
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}

                    <div className='flex gap-3'>
                        <div className="flex-1 relative">
                            <div
                                className={`relative ${isDragging ? 'ring-2 ring-white/50 rounded-lg' : ''}`}
                                onDrop={handleDrop}
                                onDragOver={handleDragOver}
                                onDragLeave={handleDragLeave}
                            >
                                {isDragging && (
                                    <div className="absolute inset-0 bg-white/10 rounded-lg pointer-events-none" />
                                )}
                                <textarea
                                    ref={textareaRef}
                                    value={text}
                                    onChange={handleTextChange}
                                    onPaste={handlePaste}
                                    onKeyDown={handleKeyDown}
                                    placeholder={
                                        isAiLoading
                                            ? "AI is thinking..."
                                            : images.length > 0
                                                ? 'Ask about your images or add more text...'
                                                : lookup.INPUT_PLACEHOLDER
                                    }
                                    disabled={isAiLoading}
                                    className={`outline-none bg-transparent w-full h-20 resize-none text-lg px-2 py-2 rounded-lg transition-opacity ${
                                        isAiLoading
                                            ? 'text-white/50 placeholder-white/30 cursor-not-allowed opacity-60'
                                            : 'text-white placeholder-white/50'
                                    }`}
                                />
                            </div>
                            <label className="absolute bottom-2 left-2 cursor-pointer group" title="Upload image">
                                <input
                                    type="file"
                                    accept=".png,.jpg,.jpeg,.svg"
                                    onChange={handleFileChange}
                                    className="opacity-0 absolute"
                                    multiple
                                />
                                <ImageIcon className="h-5 w-5 text-white/50 group-hover:text-white/80 transition-colors" />
                            </label>
                        </div>
                        {(text.trim() !== '' || images.length > 0) && (
                            <button
                                onClick={handleSubmit}
                                disabled={isAiLoading}
                                className={`p-3 rounded-md h-12 w-12 flex items-center justify-center self-end transition-colors ${
                                    isAiLoading
                                        ? 'bg-gray-400 cursor-not-allowed'
                                        : 'bg-white hover:bg-white/90 cursor-pointer'
                                }`}
                            >
                                {isAiLoading ? (
                                    <div className="w-5 h-5 border-2 border-gray-600 border-t-transparent rounded-full animate-spin"></div>
                                ) : (
                                    <ArrowRight className='h-5 w-5 text-black' />
                                )}
                            </button>
                        )}
                    </div>
                </div>
            </div>
        </div>
    )
}
 
export default ChatView