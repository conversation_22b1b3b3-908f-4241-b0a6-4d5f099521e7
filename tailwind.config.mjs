import tailwindcss from 'tailwindcss';

/** @type {import('tailwindcss').Config} */
export default {
    darkMode: ["class"],
    content: [
        './pages/**/*.{js,ts,jsx,tsx,mdx}',
        './components/**/*.{js,ts,jsx,tsx,mdx}',
        './app/**/*.{js,ts,jsx,tsx,mdx}',
        './src/**/*.{js,jsx}',
    ],
    prefix: "",
    theme: {
    	container: {
    		center: true,
    		padding: '2rem',
    		screens: {
    			'2xl': '1400px'
    		}
    	},
    	extend: {
    		colors: {
    			border: 'hsl(var(--border))',
    			input: 'hsl(var(--input))',
    			ring: 'hsl(var(--ring))',
    			background: 'hsl(var(--background))',
    			foreground: 'hsl(var(--foreground))',
    			primary: {
    				DEFAULT: 'hsl(var(--primary))',
    				foreground: 'hsl(var(--primary-foreground))'
    			},
    			secondary: {
    				DEFAULT: 'hsl(var(--secondary))',
    				foreground: 'hsl(var(--secondary-foreground))'
    			},
    			destructive: {
    				DEFAULT: 'hsl(var(--destructive))',
    				foreground: 'hsl(var(--destructive-foreground))'
    			},
    			muted: 'hsl(var(--muted))',
    			accent: {
    				DEFAULT: 'hsl(var(--accent))',
    				foreground: 'hsl(var(--accent-foreground))'
    			},
    			popover: {
    				DEFAULT: 'hsl(var(--popover))',
    				foreground: 'hsl(var(--popover-foreground))'
    			},
    			card: {
    				DEFAULT: 'hsl(var(--card))',
    				foreground: 'hsl(var(--card-foreground))'
    			},
    			brand: 'hsl(var(--brand))',
    			'brand-foreground': 'hsl(var(--brand-foreground))',
    			'muted-foreground': 'hsl(var(--muted-foreground))',
    			white: 'rgb(255 255 255)',
    			black: 'rgb(0 0 0)',
    			transparent: 'transparent',
    			'blue-300': 'rgb(147 197 253)',
    			'blue-400': 'rgb(96 165 250)',
    			'blue-500': 'rgb(59 130 246)',
    			'indigo-300': 'rgb(165 180 252)',
    			'violet-200': 'rgb(221 214 254)'
    		},
    		borderRadius: {
    			lg: 'var(--radius)',
    			md: 'calc(var(--radius) - 2px)',
    			sm: 'calc(var(--radius) - 4px)'
    		},
    		keyframes: {
    			'accordion-down': {
    				from: { height: '0' },
    				to: { height: 'var(--radix-accordion-content-height)' }
    			},
    			'accordion-up': {
    				from: { height: 'var(--radix-accordion-content-height)' },
    				to: { height: '0' }
    			},
    			appear: {
    				'0%': {
    					opacity: '0',
    					transform: 'translateY(10px)'
    				},
    				'100%': {
    					opacity: '1',
    					transform: 'translateY(0)'
    				}
    			},
    			'appear-zoom': {
    				'0%': {
    					opacity: '0',
    					transform: 'scale(0.95)'
    				},
    				'100%': {
    					opacity: '1',
    					transform: 'scale(1)'
    				}
    			},
    			aurora: {
    				from: {
    					backgroundPosition: '50% 50%, 50% 50%'
    				},
    				to: {
    					backgroundPosition: '350% 50%, 350% 50%'
    				}
    			},
    			'text-shimmer': {
    				'0%, 100%': {
    					'background-size': '200% 200%',
    					'background-position': 'left center'
    				},
    				'50%': {
    					'background-size': '200% 200%',
    					'background-position': 'right center'
    				}
    			},
          'pulse-slow': {
            '0%, 100%': {
              opacity: 1
            },
            '50%': {
              opacity: 0.8
    				}
    			}
    		},
    		animation: {
    			'accordion-down': 'accordion-down 0.2s ease-out',
    			'accordion-up': 'accordion-up 0.2s ease-out',
    			appear: 'appear 0.5s ease-out forwards',
    			'appear-zoom': 'appear-zoom 0.5s ease-out forwards',
    			aurora: 'aurora 60s linear infinite',
    			'text-shimmer': 'text-shimmer 8s infinite',
          'pulse-slow': 'pulse-slow 4s ease-in-out infinite'
    		},
    		backgroundImage: {
    			'grid-pattern': 'linear-gradient(to right, rgb(55 65 81 / 0.1) 1px, transparent 1px), linear-gradient(to bottom, rgb(55 65 81 / 0.1) 1px, transparent 1px)',
    			'grid-pattern-dark': 'linear-gradient(to right, rgb(255 255 255 / 0.05) 1px, transparent 1px), linear-gradient(to bottom, rgb(255 255 255 / 0.05) 1px, transparent 1px)',
    			'grid-pattern-light': 'linear-gradient(to right, rgb(0 0 0 / 0.05) 1px, transparent 1px), linear-gradient(to bottom, rgb(0 0 0 / 0.05) 1px, transparent 1px)'
    		}
    	}
    },
    plugins: [
        require('tailwindcss-animate'),
        function({ addBase, theme }) {
            let allColors = {};
            const colors = theme('colors');
            
            // Flatten the colors object
            for (const [key, value] of Object.entries(colors)) {
                if (typeof value === 'string') {
                    allColors[key] = value;
                } else {
                    for (const [subKey, subValue] of Object.entries(value)) {
                        if (subKey === 'DEFAULT') {
                            allColors[key] = subValue;
                        } else {
                            allColors[`${key}-${subKey}`] = subValue;
                        }
                    }
                }
            }

            // Add the color variables to :root
            addBase({
                ':root': Object.fromEntries(
                    Object.entries(allColors).map(([key, val]) => [`--${key}`, val])
                ),
            });
        },
    ],
}