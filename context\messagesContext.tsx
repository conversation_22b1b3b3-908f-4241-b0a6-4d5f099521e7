'use client'

import { create<PERSON>ontex<PERSON>, <PERSON><PERSON>atch, SetStateAction } from "react";

export interface Message {
    id: string;
    text: string;
    timestamp: number;
}

interface MessagesContextType {
    messages: Message[];
    setMessages: Dispatch<SetStateAction<Message[]>>;
}

export const MessagesContext = createContext<MessagesContextType>({
    messages: [],
    setMessages: () => {},
}); 