"use client";

import { memo, useState, useEffect } from 'react';
import { useTheme } from 'next-themes';

// Simple diff implementation
const createSimpleDiff = (beforeCode, afterCode) => {
  const beforeLines = beforeCode.split('\n');
  const afterLines = afterCode.split('\n');
  const maxLines = Math.max(beforeLines.length, afterLines.length);
  const diffLines = [];

  for (let i = 0; i < maxLines; i++) {
    const beforeLine = beforeLines[i] || '';
    const afterLine = afterLines[i] || '';
    
    if (beforeLine === afterLine) {
      diffLines.push({
        type: 'unchanged',
        beforeLineNumber: i + 1,
        afterLineNumber: i + 1,
        content: beforeLine
      });
    } else if (beforeLine && afterLine) {
      diffLines.push({
        type: 'modified',
        beforeLineNumber: i + 1,
        afterLineNumber: i + 1,
        beforeContent: beforeLine,
        afterContent: afterLine
      });
    } else if (beforeLine && !afterLine) {
      diffLines.push({
        type: 'removed',
        beforeLineNumber: i + 1,
        content: beforeLine
      });
    } else if (!beforeLine && afterLine) {
      diffLines.push({
        type: 'added',
        afterLineNumber: i + 1,
        content: afterLine
      });
    }
  }

  return diffLines;
};

const DiffLine = memo(({ line, showLineNumbers = true }) => {
  const getLineStyle = (type) => {
    switch (type) {
      case 'added':
        return 'bg-green-900/30 border-l-4 border-green-500';
      case 'removed':
        return 'bg-red-900/30 border-l-4 border-red-500';
      case 'modified':
        return 'bg-yellow-900/30 border-l-4 border-yellow-500';
      default:
        return 'bg-gray-800/20';
    }
  };

  const getLinePrefix = (type) => {
    switch (type) {
      case 'added':
        return <span className="text-green-400 mr-2">+</span>;
      case 'removed':
        return <span className="text-red-400 mr-2">-</span>;
      case 'modified':
        return <span className="text-yellow-400 mr-2">~</span>;
      default:
        return <span className="text-gray-500 mr-2"> </span>;
    }
  };

  if (line.type === 'modified') {
    return (
      <div className="font-mono text-sm">
        <div className={`flex ${getLineStyle('removed')} p-1`}>
          {showLineNumbers && (
            <span className="text-gray-500 w-12 text-right mr-4">
              {line.beforeLineNumber}
            </span>
          )}
          <span className="text-red-400 mr-2">-</span>
          <span className="text-gray-200">{line.beforeContent}</span>
        </div>
        <div className={`flex ${getLineStyle('added')} p-1`}>
          {showLineNumbers && (
            <span className="text-gray-500 w-12 text-right mr-4">
              {line.afterLineNumber}
            </span>
          )}
          <span className="text-green-400 mr-2">+</span>
          <span className="text-gray-200">{line.afterContent}</span>
        </div>
      </div>
    );
  }

  return (
    <div className={`flex font-mono text-sm ${getLineStyle(line.type)} p-1`}>
      {showLineNumbers && (
        <span className="text-gray-500 w-12 text-right mr-4">
          {line.beforeLineNumber || line.afterLineNumber || ''}
        </span>
      )}
      {getLinePrefix(line.type)}
      <span className="text-gray-200">{line.content}</span>
    </div>
  );
});

const FileHeader = memo(({ filename, hasChanges }) => {
  return (
    <div className="flex items-center bg-gray-800 p-3 border-b border-gray-600">
      <div className="flex items-center">
        <span className="text-gray-400 mr-2">📄</span>
        <span className="text-white font-medium">{filename || 'Untitled'}</span>
      </div>
      <div className="ml-auto">
        {hasChanges ? (
          <span className="text-yellow-400 text-sm">Modified</span>
        ) : (
          <span className="text-green-400 text-sm">No changes</span>
        )}
      </div>
    </div>
  );
});

const NoChangesView = memo(() => (
  <div className="h-full flex flex-col items-center justify-center p-8 text-center">
    <div className="text-6xl mb-4">✅</div>
    <h3 className="text-xl font-semibold text-white mb-2">No Changes Detected</h3>
    <p className="text-gray-400">The files are identical</p>
  </div>
));

const SimpleDiffView = memo(({ beforeCode = '', afterCode = '', filename = 'file.txt' }) => {
  const [diffLines, setDiffLines] = useState([]);
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    const diff = createSimpleDiff(beforeCode, afterCode);
    setDiffLines(diff);
    setHasChanges(diff.some(line => line.type !== 'unchanged'));
  }, [beforeCode, afterCode]);

  return (
    <div className="h-full flex flex-col bg-gray-900 text-white">
      <FileHeader filename={filename} hasChanges={hasChanges} />
      
      <div className="flex-1 overflow-auto">
        {!hasChanges ? (
          <NoChangesView />
        ) : (
          <div className="p-4">
            {diffLines.map((line, index) => (
              <DiffLine key={index} line={line} />
            ))}
          </div>
        )}
      </div>
    </div>
  );
});

// Main DiffView component that matches the expected interface
export const DiffView = memo(({ fileHistory, setFileHistory, actionRunner }) => {
  // Mock data for demonstration - replace with your actual file data
  const [selectedFile, setSelectedFile] = useState('example.js');
  const [beforeCode, setBeforeCode] = useState(`function hello() {
  console.log("Hello World");
  return true;
}`);
  
  const [afterCode, setAfterCode] = useState(`function hello() {
  console.log("Hello Universe!");
  console.log("This is a new line");
  return false;
}`);

  return (
    <div className="h-full">
      <SimpleDiffView 
        beforeCode={beforeCode}
        afterCode={afterCode}
        filename={selectedFile}
      />
    </div>
  );
});

export default DiffView;
