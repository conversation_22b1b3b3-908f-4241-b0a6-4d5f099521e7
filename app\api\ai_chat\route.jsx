import { chatSession } from '@/configs/AiModel';
import { NextResponse } from 'next/server';


export async function POST(req) {
    const {prompt, images} = await req.json();

    try {
        let result;

        if (images && images.length > 0) {
            // Send multimodal content (text + images)
            const content = [prompt, ...images];
            result = await chatSession.sendMessage(content);
        } else {
            // Send text only
            result = await chatSession.sendMessage(prompt);
        }

        const AIresp = result.response.text();
        return NextResponse.json({result: AIresp});
    } catch (error) {
        console.error('AI Chat API Error:', error);
        return NextResponse.json({error: error.message}, {status: 500});
    }
}